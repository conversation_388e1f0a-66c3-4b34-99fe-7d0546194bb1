#include <map>
#include <unordered_map>
#include <unordered_set>
#include <iostream>
#include <string>
#include <fstream>
#include <sstream>
#include <vector>
#include <algorithm>
#include <cctype>
using namespace std;

// Global variables
string filename;
int fileNum;

// User constructed functions - File Handling
string fileDetermine(int fileNum);

// User constructed functions - Error Handling
int terminate_program();
int errorCallNegative01();
int fileNotFound();
int fileNotOpen();
int terminalError();
void clearMap(unordered_map<string, vector<int> >& databaseMap, 
    vector<string>& WordClassification, vector<int>& threshold);
void tokenPushback(istream& input, string& line, 
    vector<string>& vector_of_tokens);
void createCategoryName(const vector<string>& vector_of_tokens, 
    string& categoryName);
void wordClassificationPushback(vector<string>& WordClassification, 
    const string& categoryName);
void thresholdPushback(vector<int>& threshold, int P);
void readKeywords(istream& input, int W, 
    unordered_map<string, vector<int> >& databaseMap, 
    int categoryIndex);
void processDescription(istream& input, 
    unordered_set<string>& distinctWords);
void processCount(const unordered_set<string>& distinctWords, 
    const unordered_map<string, vector<int> >& databaseMap,
    vector<int>& matchCount);
void processOutput(const vector<string>& WordClassification,
    const vector<int>& threshold, const vector<int>& matchCount,
    vector<string>& matchedCategories);
void printResult(int testCase, const vector<string>& matchedCategories);

//main - our entry point
int main(int argc, char* argv[])
{
    if (argc == 2) 
    {
        fileNum = atoi(argv[1]);
        filename = fileDetermine(fileNum);
        //Check for a valid filename
        if (filename.empty()) 
        {
            return fileNotFound();
        }
        ifstream iFile(filename);
        if (!iFile.is_open()) 
        {
            return fileNotOpen();
        }
            
        string line;
        if (!getline(iFile, line)) 
        {
            return terminate_program();
        }
            

        int T = stoi(line);

        for (int t = 0; t < T; t++) {
            unordered_map<string, vector<int> > databaseMap;
            vector<string> WordClassification;
            vector<int> threshold;
            vector<string> vector_of_tokens;
            unordered_set<string> distinctWords;
            vector<int> matchCount;
            vector<string> matchedCategories;
            string categoryName;
            
            if (!getline(iFile, line)) break;
            int C = stoi(line);

            for (int i = 0; i < C; i++) 
            {
                tokenPushback(iFile, line, vector_of_tokens);
                createCategoryName(vector_of_tokens, categoryName);
                
                int W = stoi(vector_of_tokens
                    [vector_of_tokens.size() - 2]);
                int P = stoi(vector_of_tokens
                    [vector_of_tokens.size() - 1]);
                
                wordClassificationPushback(
                    WordClassification, categoryName);
                thresholdPushback(threshold, P);
                readKeywords(iFile, W, databaseMap, i);
                vector_of_tokens.clear();
                categoryName.clear();
            }

            processDescription(iFile, distinctWords);
            matchCount.resize(WordClassification.size(), 0);
            processCount(distinctWords, databaseMap, matchCount);
            processOutput(WordClassification, 
                threshold, matchCount, matchedCategories);
            printResult(t + 1, matchedCategories);
            
            clearMap(databaseMap, WordClassification, threshold);
            distinctWords.clear();
            matchedCategories.clear();
        }
        iFile.close();
    }
    else if (argc == 1)
    {
        string line;
        if (!getline(cin, line)) 
        {
            return terminate_program();
        }

        int T = stoi(line);

        for (int t = 0; t < T; t++) {
            unordered_map<string, vector<int> > databaseMap;
            vector<string> WordClassification;
            vector<int> threshold;
            vector<string> vector_of_tokens;
            unordered_set<string> distinctWords;
            vector<int> matchCount;
            vector<string> matchedCategories;
            string categoryName;
            
            if (!getline(cin, line)) break;
            int C = stoi(line);

            for (int i = 0; i < C; i++) 
            {
                tokenPushback(cin, line, vector_of_tokens);
                createCategoryName(vector_of_tokens, categoryName);
                
                int W = stoi(vector_of_tokens
                    [vector_of_tokens.size() - 2]);
                int P = stoi(vector_of_tokens
                    [vector_of_tokens.size() - 1]);
                
                wordClassificationPushback(
                    WordClassification, categoryName);
                thresholdPushback(threshold, P);
                readKeywords(cin, W, databaseMap, i);
                vector_of_tokens.clear();
                categoryName.clear();
            }

            processDescription(cin, distinctWords);
            matchCount.resize(WordClassification.size(), 0);
            processCount(distinctWords, databaseMap, matchCount);
            processOutput(WordClassification, 
                threshold, matchCount, matchedCategories);
            printResult(t + 1, matchedCategories);
            
            clearMap(databaseMap, WordClassification, threshold);
            distinctWords.clear();
            matchedCategories.clear();
        }
    }
    else
    {
        return errorCallNegative01();
    }
    return terminate_program();
}

/*
Personally, I like using a wide variety of error handling functions. 
This is based on my SDK project, in which I am very explicit with
any error handling by shoving them into their own functions. It also
makes sure that my code is highly unique.
*/
int terminate_program()
{
    return 0;
}

int errorCallNegative01()
{
    fprintf(stderr, "Error: Negative -1 returned\n");
    return -1;
}

int fileNotFound()
{
    fprintf(stderr, "File not found!\n");
    return -1;
}

int fileNotOpen()
{
    fprintf(stderr, "File cannot be opened!\n");
    return -1;
}

int terminalError()
{
    printf("You have reached a terminal error. You've done fucked up \n");
    exit(0);
}

string fileDetermine(int fileNum)
{
    switch (fileNum)
    {
        case 1: return "crane01.txt";
        case 2: return "crane02.txt";
        case 3: return "crane03.txt";
        case 4: return "crane04.txt";
        case 5: return "crane05.txt";
        case 6: return "crane06.txt";
        case 7: return "crane07.txt";
        default: return "";
    }
}

void clearMap(unordered_map<string, vector<int> > &databaseMap, 
    vector<string> &WordClassification, vector<int> &threshold)
{
    databaseMap.clear();
    WordClassification.clear();
    threshold.clear();
}

void tokenPushback(istream &input, string &line, 
    vector<string> &vector_of_tokens)
{
    getline(input, line);
    istringstream iss(line);
    string token;
    while (iss >> token)
    {
        vector_of_tokens.push_back(token);
    }
}

void createCategoryName(const vector<string> &vector_of_tokens, 
    string &categoryName)
{
    categoryName = "";
    for (int j = 0; j < vector_of_tokens.size() - 2; j++)
    {
        if (j != 0) categoryName += " ";
        categoryName += vector_of_tokens[j];
    }
}

void wordClassificationPushback(vector<string> &WordClassification, 
    const string& categoryName)
{
    WordClassification.push_back(categoryName);
}

void thresholdPushback(vector<int> &threshold, int P)
{
    threshold.push_back(P);
}

void readKeywords(istream &input, int W, unordered_map<string, 
    vector<int> > &databaseMap, int categoryIndex)
{
    string line;
    for (int j = 0; j < W; j++)
    {
        getline(input, line);
        string word = line;
        
        transform(word.begin(), word.end(), word.begin(), ::tolower);
        while (!word.empty() && 
            !isalnum(word.front())) word.erase(0, 1);
        while (!word.empty() && 
            !isalnum(word.back())) word.pop_back();
        
        databaseMap[word].push_back(categoryIndex);
    }
}

void processDescription(istream &input, 
    unordered_set<string> &distinctWords)
{
    string line;
    while (getline(input, line)) 
    {
        if (line.empty()) 
        {
            break;
        }
        
        istringstream iss(line);
        string word;
        while (iss >> word)
        {
            transform(word.begin(), word.end(), 
                word.begin(), ::tolower);
            while (!word.empty() && 
                !isalnum(word.front())) word.erase(0, 1);
            while (!word.empty() && 
                !isalnum(word.back())) word.pop_back();
            
            distinctWords.insert(word);
        }
    }
}


void processCount(const unordered_set<string> &distinctWords, 
    const unordered_map<string, vector<int> > &databaseMap, 
    vector<int> &matchCount)
{
    for (const string& word : distinctWords) 
    {
        auto it = databaseMap.find(word);
        if (it != databaseMap.end()) 
        {
            for (int catIndex : it->second) 
            {
                matchCount[catIndex]++;
            }
        }
    }
}
void processOutput(const vector<string> &WordClassification, 
    const vector<int> &threshold, const vector<int> &matchCount, 
    vector<string> &matchedCategories)
{
    for (int i = 0; i < WordClassification.size(); i++) 
    {
        if (matchCount[i] >= threshold[i])  // Back to >=
        {
            matchedCategories.push_back(WordClassification[i]);
        }
    }
}

void printResult(int testCase, const vector<string> &matchedCategories)
{
    cout << "Test case " << testCase << " \n";
    if (matchedCategories.empty()) 
    {
        cout << "SQF Problem";
    } 
    else 
    {
        for (const string& cat : matchedCategories) 
        {
            cout << cat << " ";
        }
    }
    cout << endl;
}