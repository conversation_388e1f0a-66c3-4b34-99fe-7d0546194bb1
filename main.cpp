/*
    Name: <PERSON>, NSHE_ID_#2002519623, COURSE_SECTION 1001, ASSIGNMENT_# 4
    Description: Unordered map problem for us to solve for class
    Input: Input files that contain a variety of keywords for us to parse
    Output: Whatever is needed to satisfy the CodeGrade gods
*/
#include <map>
#include <unordered_map>
#include <unordered_set>
#include <iostream>
#include <string>
#include <fstream>
#include <sstream>
#include <vector>
#include <algorithm>
#include <cctype>
using namespace std;

// Global variables
string filename;
int fileNum;

//Handle all errors here
int terminate_program();
int errorCallNegative01();

//Map related functions
void clearMap(unordered_map<string, vector<int> >& databaseMap, 
    vector<string>& WordClassification, vector<int>& threshold);
void tokenPushback(istream& input, string& line, 
    vector<string>& vector_of_tokens);
void createCategoryName(const vector<string>& vector_of_tokens, 
    string& categoryName);
void wordClassificationPushback(vector<string>& WordClassification, 
    const string& categoryName);
void thresholdPushback(vector<int>& threshold, int P);
void readKeywords(istream& input, int W, 
    unordered_map<string, vector<int> >& databaseMap, 
    int categoryIndex);
void processDescription(istream& input, 
    unordered_set<string>& distinctWords);
void processCount(const unordered_set<string>& distinctWords, 
    const unordered_map<string, vector<int> >& databaseMap,
    vector<int>& matchCount);
void processOutput(const vector<string>& WordClassification,
    const vector<int>& threshold, const vector<int>& matchCount,
    vector<string>& matchedCategories);
void printResult(int testCase, const vector<string>& matchedCategories);

//main - our entry point
int main(int argc, char* argv[])
{
     if (argc == 1)
    {
        //We parse in the input of the file
        string line;
        if (!getline(cin, line)) 
        {
            return terminate_program();
        }
        //
        int T = stoi(line);

        //We use this to process each test case
        for (int t = 0; t < T; t++) 
        {
            //
            unordered_map<string, vector<int> > databaseMap;
            //This is where we store the category names
            vector<string> WordClassification;
            //Threshold values stored here
            vector<int> threshold;
            //We store tokens so we can parse them
            vector<string> vector_of_tokens;
            unordered_set<string> distinctWords;
            vector<int> matchCount;
            vector<string> matchedCategories;
            string categoryName;
            
            if (!getline(cin, line)) break;
            int C = stoi(line);

            for (int i = 0; i < C; i++) 
            {
                //We push back tokens into our vector of tokens
                tokenPushback(cin, line, vector_of_tokens);
                //After, we create the category name
                createCategoryName(vector_of_tokens, categoryName);
                
                int W = stoi(vector_of_tokens
                    [vector_of_tokens.size() - 2]);
                int P = stoi(vector_of_tokens
                    [vector_of_tokens.size() - 1]);
                
                wordClassificationPushback(
                    WordClassification, categoryName);
                thresholdPushback(threshold, P);
                readKeywords(cin, W, databaseMap, i);
                vector_of_tokens.clear();
                categoryName.clear();
            }

            //We process all the data related to the description
            //count, and output
            processDescription(cin, distinctWords);
            matchCount.resize(WordClassification.size(), 0);
            processCount(distinctWords, databaseMap, matchCount);
            processOutput(WordClassification, 
                threshold, matchCount, matchedCategories);
            //After we do so, we then print said result
            printResult(t + 1, matchedCategories);
            
            //Once we are done, we clear the map so that if we need
            //to use another series of inputs, we can without overflowing
            //the buffer 
            clearMap(databaseMap, WordClassification, threshold);
            distinctWords.clear();
            matchedCategories.clear();
        }
    }
    else
    {
        //It is a bit weird to do this approach, but I like naming
        //things after what they do explicitly, it's easier for me to read
        return errorCallNegative01();
    }
    return terminate_program();
}

/*
Personally, I like using a wide variety of error handling functions. 
This is based on my SDK project, in which I am very explicit with
any error handling by shoving them into their own functions. It also
makes sure that my code is highly unique.
*/
int terminate_program()
{
    return 0;
}

int errorCallNegative01()
{
    fprintf(stderr, "Error: Negative -1 returned\n");
    return -1;
}

/*
Here is where I breakup each action into its own function.
This was a much more lengthy assignment, and so this is the reason
why I have broken up everything into highly modular parts.
*/
void clearMap(unordered_map<string, vector<int> > &databaseMap, 
    vector<string> &WordClassification, vector<int> &threshold)
{
    databaseMap.clear();
    WordClassification.clear();
    threshold.clear();
}

/*
I hate having to do linked lists where the pushback happens
within a main function. This is where things go wrong, because if you 
need to manually add them, it's where things are most likely to go wrong
*/
void tokenPushback(istream &input, string &line, 
    vector<string> &vector_of_tokens)
{
    getline(input, line);
    istringstream iss(line);
    string token;
    //This just is the fastest way of pushing things back.
    while (iss >> token)
    {
        vector_of_tokens.push_back(token);
    }
}

/*
We need to store our categories as they increase, so we can
parse them later.  
*/
void createCategoryName(const vector<string> &vector_of_tokens, 
    string &categoryName)
{
    categoryName = "";
    for (int j = 0; j < vector_of_tokens.size() - 2; j++)
    {
        if (j != 0) categoryName += " ";
        categoryName += vector_of_tokens[j];
    }
}

/*
Since I've maintained doing pushback per its own functions, even though
this is an incredbly simple function, I am keeping to this schema
so it follows parallelism with my approach.
*/
void wordClassificationPushback(vector<string> &WordClassification, 
    const string& categoryName)
{
    WordClassification.push_back(categoryName);
}

void thresholdPushback(vector<int> &threshold, int P)
{
    threshold.push_back(P);
}

/*
We then use this to read in keywords by popping back the front and back
of the word.
*/
void readKeywords(istream &input, int W, unordered_map<string, 
    vector<int> > &databaseMap, int categoryIndex)
{
    string line;
    for (int j = 0; j < W; j++)
    {
        getline(input, line);
        string word = line;
        
        transform(word.begin(), word.end(), word.begin(), ::tolower);
        while (!word.empty() && 
            !isalnum(word.front())) word.erase(0, 1);
        while (!word.empty() && 
            !isalnum(word.back())) word.pop_back();
        
        databaseMap[word].push_back(categoryIndex);
    }
}

/*
We convert the description into tokens, and then we push them
back into the distinct words that it belongs to. 
*/
void processDescription(istream &input, 
    unordered_set<string> &distinctWords)
{
    string line;
    while (getline(input, line)) 
    {
        if (line.empty()) 
        {
            break;
        }
        
        istringstream iss(line);
        string word;
        while (iss >> word)
        {
            transform(word.begin(), word.end(), 
                word.begin(), ::tolower);
            while (!word.empty() && 
                !isalnum(word.front())) word.erase(0, 1);
            while (!word.empty() && 
                !isalnum(word.back())) word.pop_back();
            
            distinctWords.insert(word);
        }
    }
}

/*
We begin by seeing how many times a word appears in the database. 
When it does, our match count increases by 1 each time.
*/
void processCount(const unordered_set<string> &distinctWords,
    const unordered_map<string, vector<int> > &databaseMap,
    vector<int> &matchCount)
{
    for (const string& word : distinctWords)
    {
        auto it = databaseMap.find(word);
        if (it != databaseMap.end())
        {
            //Meow :3
            for (int catIndex : it->second)
            {
                matchCount[catIndex]++;
            }
        }
    }
}

/*
This was the problem child when it came to the issue of where instead of
SQF Problem, we were getting geometrical. We needed to make sure that
matchCount and threshold were equal, and not greater than or equal to.

I am glad that the Discord Server helped here, because I couldn't figure
it out otherwise.
*/
void processOutput(const vector<string> &WordClassification,
    const vector<int> &threshold, const vector<int> &matchCount,
    vector<string> &matchedCategories)
{
    for (int i = 0; i < WordClassification.size(); i++)
    {
        if (matchCount[i] == threshold[i]) 
        {
            matchedCategories.push_back(WordClassification[i]);
        }
    }
}

void printResult(int testCase, const vector<string> &matchedCategories)
{
    cout << "Test case " << testCase << " \n";
    if (matchedCategories.empty()) 
    {
        cout << "SQF Problem";
    } 
    else 
    {
        for (const string& cat : matchedCategories) 
        {
            cout << cat << " ";
        }
    }
    cout << endl;
}

/*
I hope that I'm doing this correct???? I am not using an ordered map
at all, but I do use part of an unordered set and I assume that this
is fine???? It didn't seem like this would be a problem?????
*/