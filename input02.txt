1
3
Math 10 5
sum
product
quotient
difference
remainder
gain
integration
derivative
factorization
primality
Physics 11 4
motion
velocity
acceleration
rate
force
mass
gravity
vacuum
displacement
work
energy
CompSci 7 7
time
computational
data
scalability
NP-Complete
undecidable
algorithm
When we want to compute the tangent line of a curve we need to take the derivative
Scalability is important when we have a lot of data to deal with especially for large applications
The sum of two numbers is the result of addition however when we subtract two numbers we obtain the difference 
Primality is a problem that is NP and Co-NP but not NP-Complete thus it takes a long time to compute
because it needs to perform prime factorization of a number since it eventually comes to a halt
it is not an undecidable problem
The halting problem is known to be an undecidable problem in computational theory thus no algorithm
can determine if a machine will ever stop on some input for some problems given enough time
Thus the halting problem is not NP-Complete which means no algorithm can exist that can decide this
in finite time
The rate of change in displacement is known as the velocity in a vacuum
everything falls at the same velocity
